%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1795359250, guid: 1cf430f187a0b40eda7f668318d8be23, type: 3}
  m_Name: MiniGameConfig
  m_EditorClassIdentifier: 
  ProjectConf:
    projectName: "\u8FDE\u8FDE\u770B\u738B\u8005"
    Appid: wx292540f88785ad37
    CDN: http://*************/lianliankan_fengling2/WebGL/1.0.6
    assetLoadType: 1
    compressDataPackage: 1
    VideoUrl: 
    DST: E:/ProjectsUnity/Lianliankan3D-fengling2/Release/wxgame
    StreamCDN: 
    bundleHashLength: 32
    bundlePathIdentifier: StreamingAssets;
    bundleExcludeExtensions: json;version
    AssetsUrl: 
    MemorySize: 256
    HideAfterCallMain: 1
    preloadFiles: bundles_effect;fairygui_battle;bundles_prefabs;bundles_fonts;bundles_scenes;bundles_textures;fairygui_common;sound_bgm
    Orientation: 0
    bgImageSrc: Assets/_MyGame/RawRes/bgText.png
    dataFileSubPrefix: 
    maxStorage: 200
    defaultReleaseSize: 31457280
    texturesHashLength: 8
    texturesPath: Assets/Textures
    needCacheTextures: 1
    loadingBarWidth: 240
    needCheckUpdate: 1
    disableHighPerformanceFallback: 0
    IOSDevicePixelRatio: 0
  SDKOptions:
    UseFriendRelation: 0
    UseCompressedTexture: 0
    UseMiniGameChat: 0
    PreloadWXFont: 0
  CompileOptions:
    DevelopBuild: 0
    AutoProfile: 0
    ScriptOnly: 0
    Il2CppOptimizeSize: 1
    profilingFuncs: 0
    Webgl2: 0
    fbslim: 1
    DeleteStreamingAssets: 1
    ProfilingMemory: 0
    CleanBuild: 0
    CustomNodePath: 
    autoAdaptScreen: 0
    showMonitorSuggestModal: 0
    enableProfileStats: 0
    enableRenderAnalysis: 0
    enablePerfAnalysis: 0
    iOSAutoGCInterval: 10000
    enableIOSPerformancePlus: 1
    brotliMT: 1
    enableWasm2023: 0
  CompressTexture:
    halfSize: 0
    useDXT5: 0
    bundleSuffix: bundle
    parallelWithBundle: 0
    bundleDir: 
    dstMinDir: 
    debugMode: 0
    force: 0
  PlayerPrefsKeys: []
  FontOptions:
    CJK_Unified_Ideographs: 1
    C0_Controls_and_Basic_Latin: 1
    CJK_Symbols_and_Punctuation: 1
    General_Punctuation: 1
    Enclosed_CJK_Letters_and_Months: 1
    Vertical_Forms: 1
    CJK_Compatibility_Forms: 1
    Miscellaneous_Symbols: 1
    CJK_Compatibility: 1
    Halfwidth_and_Fullwidth_Forms: 1
    Dingbats: 1
    Letterlike_Symbols: 1
    Enclosed_Alphanumerics: 1
    Number_Forms: 1
    Currency_Symbols: 1
    Arrows: 1
    Geometric_Shapes: 1
    Mathematical_Operators: 1
    CustomUnicode: 
